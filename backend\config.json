{"api": {"url": "https://openrouter.ai/api/v1/chat/completions", "headers": {"Content-Type": "application/json", "Authorization": "Bearer 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"}, "model_name": "deepseek/deepseek-r1-0528:free", "timeout": 60, "max_retries": 3, "retry_delay": 10}, "analysis": {"personality_only": false, "available_dimensions": ["性格特征", "事业发展", "财运分析", "感情婚姻", "健康状况", "人际关系", "学业教育", "家庭关系"], "enabled_dimensions": ["性格特征", "事业发展", "财运分析", "感情婚姻", "健康状况", "人际关系", "学业教育", "家庭关系"]}, "paths": {"prompts_dir": "prompts", "output_dir": "summaries", "logs_dir": "llm_logs"}, "server": {"host": "0.0.0.0", "port": 5000, "debug": false}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_enabled": true, "console_enabled": true}, "card_validation": {"enabled": true, "valid_cards": ["yUqT8P5XewvanDgi", "NWDT1fTupGJiyy7B", "N6oYJcNVmv3orFkf", "bCpwMmO4BXZ31hIv", "RWICouEJaClKr72b", "kfpJQ0gd2huaKJBA", "Ke6EA8CXfvSfcUGj", "TKS0EJzL2WqABFH5", "rWDvohqEHeFjz9L1", "DAnrKv2ffcbvfh0C", "dGz05ASuSNo9DkuA", "KmIxSzZKTdKXMRlD", "NI45OgqRJEf7KZBonjMXLX7W", "e0zJP8ArExwu200CIYoePkoM", "kFnt5fKwQkJq21Hnpwn3KJqkc60UAf7z", "FzxCXQ3SrEPHLAMDD7E1ybCpPyow1kfv", "wdd", "5bkbHk6ejeEjQ6izbkX2DzkXaff8DoZp", "szWoxsEhheNM2aGL", "Voy3Lj7EMaomuQDs", "UL3hfhUOOKMEY1pK", "3fE8B54OOfA5OiPh", "f3uFBTZJlhVKHmwx", "vJXF2he7y7atI1EM", "3LMUW3LxTH1sH1il", "FFhSM3TrzTtqL8RX", "WG5GrAri6dnTHF5I", "lrI54FVZ4eWkbonw", "HexWO014RxBXwBbB", "Xs1MR9iVx9RNZOk6", "q1szd9wKXLgcbN4e"]}, "webhook": {"secret_key": "a7712e3b-7f96-4b2e-9573-a6f87d9fd848", "enabled": true}}